@using Microsoft.AspNetCore.Components.Web
@inherits LayoutComponentBase

<div class="page">
    <main>
        @Body
    </main>
</div>

@*<div id="blazor-error-ui">
    An unhandled error has occurred.
    <a href="" class="reload">Reload</a>
    <a class="dismiss">🗙</a>
</div>*@

<style>
    .page {
        position: relative;
        display: flex;
        flex-direction: column;
        min-height: 100vh;
    }

    main {
        flex: 1;
    }

    #blazor-error-ui {
        background: lightyellow;
        bottom: 0;
        box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.2);
        display: none;
        left: 0;
        padding: 0.6rem 1.25rem 0.7rem 1.25rem;
        position: fixed;
        width: 100%;
        z-index: 1000;
    }

    #blazor-error-ui .dismiss {
        cursor: pointer;
        position: absolute;
        right: 0.75rem;
        top: 0.5rem;
    }

    .certificate-container {
        max-width: 800px;
        margin: 10px auto;
        padding: 10px;
        font-family: Arial, sans-serif;
        background: white;
    }

    .certificate-header {
        text-align: center;
        margin-bottom: 20px;
        border-bottom: 2px solid #333;
        padding-bottom: 10px;
    }

    .certificate-header h1 {
        margin: 0;
        color: #333;
        font-size: 2.5em;
    }

    .date {
        margin: 5px 0 0 0;
        font-size: 1em;
        color: #666;
    }

    .certificate-body {
        margin-bottom: 20px;
    }

    .section-title {
        font-size: 1.4em;
        font-weight: bold;
        color: #333;
        margin: 15px 0 8px 0;
        padding-bottom: 5px;
        border-bottom: 1px solid #ccc;
    }

    .employee-info, .salary-info {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
        margin-bottom: 15px;
    }

    .info-section {
        display: flex;
        flex-direction: column;
        gap: 10px;
    }

    .info-row {
        display: flex;
        justify-content: space-between;
        padding: 8px 0;
        border-bottom: 1px dotted #ccc;
    }

    .info-label {
        font-weight: bold;
        color: #333;
        min-width: 150px;
    }

    .info-value {
        color: #666;
        text-align: right;
    }

    .certificate-footer {
        margin-top: 25px;
        padding-top: 15px;
        border-top: 1px solid #333;
    }

    .signature-section {
        display: flex;
        justify-content: space-between;
        margin-bottom: 15px;
    }

    .signature {
        text-align: center;
        width: 200px;
    }

    .signature-line {
        border-bottom: 1px solid #333;
        margin-bottom: 5px;
        height: 50px;
    }

    .disclaimer {
        text-align: center;
        font-style: italic;
        color: #666;
        margin-top: 10px;
    }

    .print-actions {
        text-align: center;
        margin-top: 15px;
        gap: 10px;
        display: flex;
        justify-content: center;
    }

    /*@@media print {
        .print-actions {
            display: none !important;
        }

        .certificate-container {
            margin: 0;
            padding: 0;
            max-width: none;
        }

        body {
            margin: 0;
            padding: 20px;
        }
    }

    @@media (max-width: 768px) {
        .employee-info, .salary-info {
            grid-template-columns: 1fr;
        }

        .signature-section {
            flex-direction: column;
            gap: 30px;
        }

        .print-actions {
            flex-direction: column;
            align-items: center;
        }
    }*/
</style>
