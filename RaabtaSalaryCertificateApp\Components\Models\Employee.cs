namespace RaabtaSalaryCertificateApp.Components.Models;

public class Employee
{
    public string? EmployeeCode { get; set; }
    public string? EmployeeName { get; set; }
    public string? FatherName { get; set; }
    public DateTime? DateOfJoin { get; set; }
    public string? Department { get; set; }
    public string? Designation { get; set; }
    public string? Station { get; set; }
    public string? EmploymentStatus { get; set; }
    public string? BasicSalary { get; set; }
    public string? GrossSalary { get; set; }
    public string? SalaryType { get; set; }
    public string? Currency { get; set; }
    public string? HouseRent { get; set; }
    public string? Utilities { get; set; }
    public string? PetrolEntitlement { get; set; }
    public string? MobileEntitlement { get; set; }
    public string? CarConveyance { get; set; }
    public string? CarDescription { get; set; }
    public string? Other { get; set; }
    public string? CNIC { get; set; }
    public string? Gender { get; set; }
    public string? EmploymentType { get; set; }
}