@page "/employment-certificate/{EmployeeCode}"
@using RaabtaSalaryCertificateApp.Components.Models
@using RaabtaSalaryCertificateApp.Components.Services
@using Microsoft.AspNetCore.Authorization
@attribute [Authorize]
@inject RaabtaDataService RaabtaDataService
@inject IJSRuntime JSRuntime
@inject NavigationManager NavigationManager
@layout Layout.BlankLayout

<PageTitle>Employment Certificate</PageTitle>

<div class="certificate-container">
    @if (employee != null)
    {
        var genderPronoun = GetGenderPronoun("He", "She");
        var genderPossessive = GetGenderPronoun("his", "her");
        <div style="max-width: 700px; margin: 0 auto; padding: 40px; border: 1px solid #ccc; font-family: 'Segoe UI', Arial, sans-serif; background: #fff;">
            <div style="text-align: center; font-weight: bold; letter-spacing: 2px; margin-bottom: 20px;">------------------------------</div>
            <div style="height: 20px;"></div>
            <div style="text-align: center; font-size: 1.5em; font-weight: bold; margin-bottom: 10px;">TO WHOM IT MAY CONCERN</div>
            <div style="text-align: center; font-weight: bold; letter-spacing: 2px; margin-bottom: 20px;">----------------------</div>
            <div style="height: 20px;"></div>
            <div style="text-align: center; font-size: 1.1em; font-weight: bold; margin-bottom: 30px;">
                <span style="font-style: italic;">@DateTime.Now.ToString("MMMM dd, yyyy")</span>
            </div>
            <div style="height: 20px;"></div>
            <div style="font-size: 1.1em; line-height: 2; text-align: justify;">
                <span style="font-style: italic;">This is to certify that </span>
                @if (employee.Gender.ToLower() == "male")
                {
                    <span style="font-weight: bold; font-style: italic;">Mr. @employee.EmployeeName @GetRelationPrefix() @employee.FatherName&nbsp;</span>
                }
                else
                {
            <span style="font-weight: bold; font-style: italic;">Ms. @employee.EmployeeName&nbsp;</span>
                }
                <span style="font-style: italic;">bearing Employee Code </span>
                <span style="font-style: italic;font-weight:bold">@employee.EmployeeCode</span>,
                <span style="font-style: italic;">&nbsp;holding CNIC <b>@employee.CNIC</b> is working in this organization since <b>@(employee.DateOfJoin?.ToString("dd-MMM-yyyy") ?? "N/A")</b>. @genderPronoun is a</span>
                <span style="font-weight: bold; font-style: italic;">@employee.EmploymentType</span>
                <span style="font-style: italic;">employee of this organization &amp; presently performing @genderPossessive duties as</span>
                <span style="font-weight: bold; font-style: italic;">@employee.Designation</span><span style="font-style: italic;">.</span>
                <span style="font-style: italic;"> </span>
                <p>&nbsp;</p>
                <span style="font-style: italic;">This certificate has been issued on @genderPossessive request. Please do not hesitate to contact undersigned for any assistance and queries in this regard.</span>
            </div>
            <div style="height: 30px;"></div>
            <div style="font-style: italic;">Regards,</div>
            <div style="height: 30px;"></div>
            <div style="font-weight: bold; letter-spacing: 2px;">_____________________________</div>
            <div style="font-weight: bold; font-style: italic;">Muhammad Faheem</div>
            <div style="font-weight: bold; font-style: italic;">Manager Human Resources</div>
            <div style="height: 20px;"></div>
            <div>Email: <EMAIL></div>
            <div>CC: Personnel File</div>
            <div style="text-align: center; font-weight: bold; letter-spacing: 2px; margin-top: 30px;">--------------------</div>
        </div>

            }
    else
    {
        <p>Loading employee data...</p>
    }
</div>


@code {
    [Parameter]
    public string? EmployeeCode { get; set; }

    [CascadingParameter] private Task<AuthenticationState>? authenticationStateTask { get; set; }

    private Employee? employee;
    private string _userId = "";

    protected override async Task OnInitializedAsync()
    {
        if (authenticationStateTask != null)
        {
            var user = (await authenticationStateTask).User;
            if (user.Identity is { Name: not null }) _userId = user.Identity.Name;

            if (!string.IsNullOrEmpty(_userId))
            {
                // Check if user has access to employment certificates
                var canAccess = await RaabtaDataService.CanAccessCertificate(_userId, "Employment");
                if (!canAccess)
                {
                    NavigationManager.NavigateTo("/not-authorized");
                    return;
                }
            }
        }

        var employees = await RaabtaDataService.LoadEmployeesFromExcel(EmployeeCode ?? "");
        employee = employees.FirstOrDefault(e => e.EmployeeCode == EmployeeCode);
    }

    private string GetRelationPrefix()
    {
        if (employee?.EmployeeName == null) return "S/o";
        
        // Simple gender detection based on common patterns
        var name = employee.EmployeeName.ToLower();
        
        // Common female name endings/patterns
        //if (name.EndsWith("a") || name.EndsWith("ah") || name.EndsWith("een") || 
        //    name.EndsWith("ana") || name.EndsWith("iya") || name.EndsWith("uma") ||
        //    name.Contains("fatima") || name.Contains("ayesha") || name.Contains("khadija") ||
        //    name.Contains("zainab") || name.Contains("maria") || name.Contains("sara"))
        if(employee.Gender.ToLower()=="female")
        {
            return "D/o";
        }
        
        return "S/o"; // Default to male
    }

    private string GetGenderPronoun(string maleForm, string femaleForm)
    {
        return GetRelationPrefix() == "D/o" ? femaleForm : maleForm;
    }

    private async Task PrintCertificate()
    {
        try
        {
            await JSRuntime.InvokeVoidAsync("window.print");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Print error: {ex.Message}");
        }
    }

    private void GoBack()
    {
        _ = JSRuntime.InvokeVoidAsync("window.close");
    }
}
