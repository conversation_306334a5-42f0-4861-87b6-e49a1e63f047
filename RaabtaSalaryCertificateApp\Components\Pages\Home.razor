@page "/"
@using Microsoft.AspNetCore.Authorization
@using RaabtaSalaryCertificateApp.Components.Models
@using RaabtaSalaryCertificateApp.Components.Services
@using FilterType = Syncfusion.Blazor.Grids.FilterType
@attribute [Authorize]
@inject RaabtaDataService RaabtaDataService
@inject NavigationManager NavigationManager
@rendermode InteractiveServer

<PageTitle>Employee List</PageTitle>

<h1>Employee List - @filteredEmployees.Count</h1>

<div class="filter-section">
    <div class="row mb-3">
        <div class="col-md-6">
            <div class="input-group">
                <span class="input-group-text">
                    <FluentIcon Value="@(new Icons.Regular.Size16.Search())" />
                </span>
                <input type="text" class="form-control" placeholder="Search by name, code, CNIC, department, designation, or station..."
                       @bind="searchText" @bind:event="oninput" />
                @if (!string.IsNullOrEmpty(searchText))
                {
                    <button class="btn btn-outline-secondary" type="button" @onclick="ClearSearch">
                        <FluentIcon Value="@(new Icons.Regular.Size16.Dismiss())" />
                    </button>
                }
            </div>
            @if (!string.IsNullOrEmpty(searchText))
            {
                <small class="text-muted">Showing @filteredEmployees.Count of @allEmployees.Count employees</small>
            }
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md">
        @if (filteredEmployees != null && filteredEmployees.Any())
        {
            <SfGrid DataSource="filteredEmployees" AllowFiltering="true" AllowSorting="true" Height="calc(100vh - 280px)" EnableColumnVirtualization="true">
                <GridFilterSettings Type="FilterType.Excel"></GridFilterSettings>
                <GridColumns>
                    <GridColumn HeaderText="Actions" AutoFit="true">
                        <Template Context="cc">
                            @{
                                if (cc is Employee mm)
                                {
                                    var uri = $"salary-certificate/{mm.EmployeeCode}";
                                    var uri2 = $"employment-certificate/{mm.EmployeeCode}";
                                    var trg = $"sc-{mm.EmployeeCode}";
                                    var trg2 = $"ec-{mm.EmployeeCode}";

                                    bool hasAnyAccess = false;

                                    @if (canAccessSalary)
                                    {
                                        <a href="@uri" target="@trg">Salary Certificate</a>
                                        hasAnyAccess = true;
                                    }

                                    @if (canAccessSalary && canAccessEmployment)
                                    {
                                        <span> | </span>
                                    }

                                    @if (canAccessEmployment)
                                    {
                                        <a href="@uri2" target="@trg2">Employment Certificate</a>
                                        hasAnyAccess = true;
                                    }

                                    @if (!hasAnyAccess)
                                    {
                                        <span class="text-muted">No Access</span>
                                    }
                                }
                            }

                        </Template>
                    </GridColumn>
                    <GridColumn HeaderText="Code" Field="@nameof(Employee.EmployeeCode)" AutoFit="true"></GridColumn>
                    <GridColumn HeaderText="Name" Field="@nameof(Employee.EmployeeName)" AutoFit="true"></GridColumn>
                    <GridColumn HeaderText="Father Name" Field="@nameof(Employee.FatherName)" AutoFit="true"></GridColumn>
                    <GridColumn HeaderText="Gender" Field="@nameof(Employee.Gender)" AutoFit="true"></GridColumn>
                    <GridColumn HeaderText="Date of Joining" Field="@nameof(Employee.DateOfJoin)" Format="d-MMM-yyyy" AutoFit="true"></GridColumn>
                    <GridColumn HeaderText="Department" Field="@nameof(Employee.Department)" Format="d-MMM-yyyy" AutoFit="true"></GridColumn>
                    <GridColumn HeaderText="Designation" Field="@nameof(Employee.Designation)" Format="d-MMM-yyyy" AutoFit="true"></GridColumn>
                    <GridColumn HeaderText="Station" Field="@nameof(Employee.Station)" Format="d-MMM-yyyy" AutoFit="true"></GridColumn>
                    <GridColumn HeaderText="CNIC" Field="@nameof(Employee.CNIC)" Format="d-MMM-yyyy" AutoFit="true"></GridColumn>
                </GridColumns>
            </SfGrid>
        }
    </div>
</div>

@code {
    [CascadingParameter] private Task<AuthenticationState>? authenticationStateTask { get; set; }
    private string _userId = "";

    private PaginationState pagination = new() { ItemsPerPage = 10 };
    private List<Employee> allEmployees = new();
    private List<Employee> filteredEmployees = new();
    

    private bool canAccessSalary = false;
    private bool canAccessEmployment = false;

    protected override async Task OnInitializedAsync()
    {
        if (authenticationStateTask != null)
        {
            var user = (await authenticationStateTask).User;
            if (user.Identity is { Name: not null }) _userId = user.Identity.Name;

            if (!string.IsNullOrEmpty(_userId))
            {
                // Check user permissions for different certificate types
                canAccessSalary = await RaabtaDataService.CanAccessCertificate(_userId, "Salary");
                canAccessEmployment = await RaabtaDataService.CanAccessCertificate(_userId, "Employment");

                // Get all certificate types user has access to
                

                // Load employees only if user has any certificate access
                if (canAccessSalary || canAccessEmployment)
                {
                    allEmployees = await RaabtaDataService.LoadEmployeesFromExcel("");
                    FilterEmployees();
                }
                else
                {
                    // Redirect to not authorized page if no access
                    NavigationManager.NavigateTo("/not-authorized");
                }
            }
        }
    }

    private string _searchText = "";
    private string searchText
    {
        get => _searchText;
        set
        {
            _searchText = value;
            FilterEmployees();
        }
    }

    private void ClearSearch()
    {
        searchText = "";
    }

    private void FilterEmployees()
    {
        if (string.IsNullOrWhiteSpace(searchText))
        {
            filteredEmployees = allEmployees;
        }
        else
        {
            var searchTerm = searchText.ToLowerInvariant();
            filteredEmployees = allEmployees.Where(emp =>
                (emp.EmployeeName?.ToLowerInvariant().Contains(searchTerm) ?? false) ||
                (emp.EmployeeCode?.ToLowerInvariant().Contains(searchTerm) ?? false) ||
                (emp.CNIC?.ToLowerInvariant().Contains(searchTerm) ?? false) ||
                (emp.Department?.ToLowerInvariant().Contains(searchTerm) ?? false) ||
                (emp.Designation?.ToLowerInvariant().Contains(searchTerm) ?? false) ||
                (emp.Station?.ToLowerInvariant().Contains(searchTerm) ?? false)
            ).ToList();
        }
        StateHasChanged();
    }

    private void PrintSalaryCertificate(Employee employee)
    {
        if (!canAccessSalary)
        {
            NavigationManager.NavigateTo("/not-authorized");
            return;
        }

        // Navigate to the salary certificate page with the employee ID in a new window
        var uri = $"salary-certificate/{employee.EmployeeCode}";
        NavigationManager.NavigateTo(uri, true);
    }

    private void PrintEmploymentCertificate(Employee employee)
    {
        if (!canAccessEmployment)
        {
            NavigationManager.NavigateTo("/not-authorized");
            return;
        }

        // Navigate to the employment certificate page with the employee ID in a new window
        var uri = $"employment-certificate/{employee.EmployeeCode}";
        NavigationManager.NavigateTo(uri, true);
    }

}