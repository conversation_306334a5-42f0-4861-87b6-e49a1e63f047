@page "/"
@using Microsoft.AspNetCore.Authorization
@using RaabtaSalaryCertificateApp.Components.Models
@using RaabtaSalaryCertificateApp.Components.Services
@using FilterType = Syncfusion.Blazor.Grids.FilterType
@attribute [Authorize]
@inject RaabtaDataService RaabtaDataService
@inject NavigationManager NavigationManager
@rendermode InteractiveServer

<PageTitle>Employee List</PageTitle>

<h1>Employee List - @allEmployees.Count</h1>
<div class="row">
    <div class="col-md">
        @if (allEmployees != null && allEmployees.Any())
        {
            <SfGrid DataSource="allEmployees" AllowFiltering="true" AllowSorting="true" Height="calc(100vh - 220px)" EnableColumnVirtualization="true">
                <GridFilterSettings Type="FilterType.Excel"></GridFilterSettings>
                <GridColumns>
                    <GridColumn HeaderText="Actions" AutoFit="true">
                        <Template Context="cc">
                            @{
                                if (cc is Employee mm)
                                {
                                    var uri = $"salary-certificate/{mm.EmployeeCode}";
                                    var uri2 = $"employment-certificate/{mm.EmployeeCode}";
                                    var trg = $"sc-{mm.EmployeeCode}";
                                    var trg2 = $"ec-{mm.EmployeeCode}";
                                    <a href="@uri" target="@trg">Salary Certificate</a>
                                    <span>|</span>
                                    <a href="@uri2" target="@trg2">Employment Certificate</a>
                                }
                            }

                        </Template>
                    </GridColumn>
                    <GridColumn HeaderText="Code" Field="@nameof(Employee.EmployeeCode)" AutoFit="true"></GridColumn>
                    <GridColumn HeaderText="Name" Field="@nameof(Employee.EmployeeName)" AutoFit="true"></GridColumn>
                    <GridColumn HeaderText="Father Name" Field="@nameof(Employee.FatherName)" AutoFit="true"></GridColumn>
                    <GridColumn HeaderText="Gender" Field="@nameof(Employee.Gender)" AutoFit="true"></GridColumn>
                    <GridColumn HeaderText="Date of Joining" Field="@nameof(Employee.DateOfJoin)" Format="d-MMM-yyyy" AutoFit="true"></GridColumn>
                    <GridColumn HeaderText="Department" Field="@nameof(Employee.Department)" Format="d-MMM-yyyy" AutoFit="true"></GridColumn>
                    <GridColumn HeaderText="Designation" Field="@nameof(Employee.Designation)" Format="d-MMM-yyyy" AutoFit="true"></GridColumn>
                    <GridColumn HeaderText="Station" Field="@nameof(Employee.Station)" Format="d-MMM-yyyy" AutoFit="true"></GridColumn>
                    <GridColumn HeaderText="CNIC" Field="@nameof(Employee.CNIC)" Format="d-MMM-yyyy" AutoFit="true"></GridColumn>
                </GridColumns>
            </SfGrid>
        }
    </div>
</div>

@code {
    [CascadingParameter] private Task<AuthenticationState>? authenticationStateTask { get; set; }
    private string _userId = "";

    private FluentDataGrid<Employee>? dataGrid;
    private PaginationState pagination = new() { ItemsPerPage = 10 };
    private List<Employee> allEmployees = new();

    private string searchText = "";

    protected override async Task OnInitializedAsync()
    {
        
    }

    protected override void OnInitialized()
    {
        base.OnInitialized();

        if (authenticationStateTask != null)
        {
            var user = (authenticationStateTask).Result.User;
            if (user.Identity is { Name: not null }) _userId = user.Identity.Name;
            if (!string.IsNullOrEmpty(_userId))
            {

                var canAccess = RaabtaDataService.CanAccessCertificate(_userId, "Salary").Result;
                
                // allEmployees = RaabtaDataService.LoadEmployeesFromExcel("").Result;

                
            }
        }
    }


    private void PrintSalaryCertificate(Employee employee)
    {
        // Navigate to the salary certificate page with the employee ID in a new window
        var uri = $"salary-certificate/{employee.EmployeeCode}";
        NavigationManager.NavigateTo(uri, true);
    }

}