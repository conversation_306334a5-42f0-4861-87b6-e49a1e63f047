@page "/not-authorized"
@using Microsoft.AspNetCore.Authorization
@attribute [Authorize]

<PageTitle>Not Authorized</PageTitle>

<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-6 text-center">
            <div class="alert alert-danger" role="alert">
                <h1 class="display-4">
                    <FluentIcon Value="@(new Icons.Regular.Size48.Shield())" Color="Color.Error" />
                </h1>
                <h2 class="alert-heading">Access Denied</h2>
                <p class="lead">You do not have permission to access this certificate type.</p>
                <hr>
                <p class="mb-0">
                    Please contact your administrator if you believe you should have access to this resource.
                </p>
                <div class="mt-4">
                    <a href="/" class="btn btn-primary">
                        <FluentIcon Value="@(new Icons.Regular.Size16.Home())" />
                        Return to Home
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    [CascadingParameter] private Task<AuthenticationState>? authenticationStateTask { get; set; }
    private string _userId = "";

    protected override void OnInitialized()
    {
        base.OnInitialized();

        if (authenticationStateTask != null)
        {
            var user = (authenticationStateTask).Result.User;
            if (user.Identity is { Name: not null }) _userId = user.Identity.Name;
        }
    }
}
