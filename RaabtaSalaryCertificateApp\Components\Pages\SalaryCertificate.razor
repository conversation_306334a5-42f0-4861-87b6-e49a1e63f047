@page "/salary-certificate/{EmployeeCode}"
@using RaabtaSalaryCertificateApp.Components.Models
@using RaabtaSalaryCertificateApp.Components.Services
@inject RaabtaDataService RaabtaDataService
@inject IJSRuntime JSRuntime
@inject NavigationManager NavigationManager
@layout Layout.BlankLayout

<PageTitle>Salary Certificate</PageTitle>

<div class="certificate-container">
    @if (employee != null)
    {
        var genderPronoun = employee.Gender?.ToLower() == "female" ? "She" : "He";
        var genderPronounLower = employee.Gender?.ToLower() == "female" ? "she" : "he";
        var genderPossessive = employee.Gender?.ToLower() == "female" ? "her" : "his";
        var genderPossessiveUpper = employee.Gender?.ToLower() == "female" ? "Her" : "His";
        var sodo = "S/o";
        if (employee.Gender.ToLower() == "female") sodo = "D/o";
        <div style="max-width: 700px; margin: 0 auto; padding: 40px; border: 1px solid #ccc; font-family: 'Segoe UI', Arial, sans-serif; background: #fff;">
            <div style="text-align: center; font-weight: bold; letter-spacing: 2px; margin-bottom: 20px;">------------------------------</div>
            <div style="height: 20px;"></div>
            <div style="text-align: center; font-size: 1.5em; font-weight: bold; margin-bottom: 10px;">TO WHOM IT MAY CONCERN</div>
            <div style="text-align: center; font-weight: bold; letter-spacing: 2px; margin-bottom: 20px;">----------------------</div>
            <div style="height: 20px;"></div>
            <div style="text-align: center; font-size: 1.1em; font-weight: bold; margin-bottom: 30px;">
                <span style="font-style: italic;">@DateTime.Now.ToString("MMMM dd, yyyy")</span>
            </div>
            <div style="height: 20px;"></div>
            <div style="font-size: 1.1em; line-height: 2; text-align: justify;">
                <span style="font-style: italic;">This is to certify that </span>
                @if (employee.Gender.ToLower() == "male")
                {
                    <span style="font-weight: bold; font-style: italic;">Mr. @employee.EmployeeName @sodo @employee.FatherName&nbsp;</span>
                }
                else
                {
                    <span style="font-weight: bold; font-style: italic;">Ms. @employee.EmployeeName&nbsp;</span>
                }
                <span style="font-style: italic;">bearing Employee Code </span>
                <span style="font-style: italic;font-weight:bold">@employee.EmployeeCode</span>,
                <span style="font-style: italic;">
                    &nbsp;holding CNIC <b>@employee.CNIC</b> is working in this organization since <b>@employee.DateOfJoin?.ToString("dd-MMM-yyyy")</b>.
                    @genderPronoun is a @employee.EmploymentType
                </span>
                <span style="font-weight: bold; font-style: italic;">@employee.EmploymentStatus</span>
                <span style="font-style: italic;">employee of this organization &amp; presently performing @genderPossessive duties as</span>
                <span style="font-weight: bold; font-style: italic;">@employee.Designation</span>
                <span style="font-style: italic;">&amp; drawing a salary of @employee.Currency. @employee.GrossSalary/- per month.</span>
                @if (!string.IsNullOrWhiteSpace(employee.CarConveyance) || !string.IsNullOrWhiteSpace(employee.PetrolEntitlement) || !string.IsNullOrWhiteSpace(employee.MobileEntitlement))
                {
                    <span style="font-style: italic;">
                        &nbsp; Moreover, @genderPronounLower is entitled to
                        @if (!string.IsNullOrWhiteSpace(employee.CarConveyance) && employee.CarConveyance != "0")
                        {
                            <span>Monthly Vehicle Allowance of Rs. @employee.CarConveyance/-</span>
                        }
                        @if (!string.IsNullOrWhiteSpace(employee.CarConveyance) && !string.IsNullOrWhiteSpace(employee.PetrolEntitlement))
                        {
                            <span> along with </span>
                        }
                        @if (!string.IsNullOrWhiteSpace(employee.PetrolEntitlement) && employee.PetrolEntitlement != "0")
                        {
                            <span> @employee.PetrolEntitlement ltr of Fuel per month</span>
                        }
                        @if ((!string.IsNullOrWhiteSpace(employee.CarConveyance) || !string.IsNullOrWhiteSpace(employee.PetrolEntitlement)) && !string.IsNullOrWhiteSpace(employee.MobileEntitlement) && employee.PetrolEntitlement != "0")
                        {
                            <span> &amp; </span>
                        }
                        @if (!string.IsNullOrWhiteSpace(employee.MobileEntitlement))
                        {
                            <span>Mobile Allowance of Rs. @employee.MobileEntitlement/- per month</span>
                        }
                        <span>.</span>
                    </span>
                }
                <span style="font-style: italic;"> </span>
                <p>&nbsp;</p>
                <span style="font-style: italic;">This certificate has been issued on @genderPossessive request. Please do not hesitate to contact undersigned for any assistance and queries in this regard.</span>
            </div>
            <div style="height: 30px;"></div>
            <div style="font-style: italic;">Regards,</div>
            <div style="height: 30px;"></div>
            <div style="font-weight: bold; letter-spacing: 2px;">_____________________________</div>
            <div style="font-weight: bold; font-style: italic;">Muhammad Faheem</div>
            <div style="font-weight: bold; font-style: italic;">Manager Human Resources</div>
            <div style="height: 20px;"></div>
            <div>Email: <EMAIL></div>
            <div>CC: Personnel File</div>
            <div style="text-align: center; font-weight: bold; letter-spacing: 2px; margin-top: 30px;">--------------------</div>
        </div>
    }
    else
    {
        <p>Loading employee data...</p>
    }
</div>

@code {
    [Parameter]
    public string? EmployeeCode { get; set; }

    private Employee? employee;

    protected override async Task OnInitializedAsync()
    {
        employee = (await RaabtaDataService.LoadEmployeesFromExcel(EmployeeCode)).FirstOrDefault();
        
    }

    private async Task PrintCertificate()
    {
        try
        {
            await JSRuntime.InvokeVoidAsync("window.print");
        }
        catch (Exception ex)
        {
            // Handle any print errors gracefully
            Console.WriteLine($"Print error: {ex.Message}");
        }
    }

    private void GoBack()
    {
        // Close the current window/tab
        _ = JSRuntime.InvokeVoidAsync("window.close");
    }
}