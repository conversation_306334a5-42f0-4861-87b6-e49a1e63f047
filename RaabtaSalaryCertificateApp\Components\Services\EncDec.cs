// 
//    This sample code is provided "AS IS" with no warranties,
//    and confers no rights. 
// 
//    Updated for .NET Core 9 from original ASP.NET 1.1 version
//    - Replaced Rijndael with Aes (recommended modern equivalent)
//    - Replaced PasswordDeriveBytes with Rfc2898DeriveBytes
//    - Added proper using statements for resource disposal
//    - Maintained backward compatibility with original functionality
// 
// string grossSal=EncDec.Encrypt(GrossSal,"a0uJ4P8%H&9P!bH8em@E#J>s");

using System;
using System.IO;
using System.Security.Cryptography;

namespace RaabtaSalaryCertificateApp.Components.Services;

public class EncDec
{
    // Salt used for key derivation (same as original for compatibility)
    private static readonly byte[] Salt = new byte[]
    {
            0x49, 0x76, 0x61, 0x6e, 0x20, 0x4d,
            0x65, 0x64, 0x76, 0x65, 0x64, 0x65, 0x76
    };

    // Get key and IV exactly as the original code did
    private static void GetKeyAndIV(string password, byte[] salt, out byte[] key, out byte[] iv)
    {
        // Use the deprecated but backward-compatible approach
        // This creates the same key derivation as the original PasswordDeriveBytes
#pragma warning disable SYSLIB0041 // Type or member is obsolete
        using (var pdb = new PasswordDeriveBytes(password, salt))
        {
            key = pdb.GetBytes(32);  // First call gets bytes 0-31
            iv = pdb.GetBytes(16);   // Second call gets bytes 32-47
        }
#pragma warning restore SYSLIB0041 // Type or member is obsolete
    }

    // Encrypt a byte array into a byte array using a key and an IV 
    public static byte[] Encrypt(byte[] clearData, byte[] Key, byte[] IV)
    {
        // Create a MemoryStream to accept the encrypted bytes 
        using (var ms = new MemoryStream())
        {
            // Create AES algorithm (modern replacement for Rijndael)
            // AES is essentially Rijndael with fixed block size of 128 bits
            using (var aes = Aes.Create())
            {
                // Set the key and IV
                aes.Key = Key;
                aes.IV = IV;

                // Create a CryptoStream for encryption
                using (var cs = new CryptoStream(ms, aes.CreateEncryptor(), CryptoStreamMode.Write))
                {
                    // Write the data and perform encryption
                    cs.Write(clearData, 0, clearData.Length);
                    cs.FlushFinalBlock();
                }

                // Return the encrypted data
                return ms.ToArray();
            }
        }
    }

    // Encrypt a string into a string using a password 
    public static string Encrypt(string clearText, string Password)
    {
        // Convert string to byte array using Unicode encoding (same as original)
        byte[] clearBytes = System.Text.Encoding.Unicode.GetBytes(clearText);

        // Get key and IV exactly as original code did
        GetKeyAndIV(Password, Salt, out byte[] key, out byte[] iv);

        byte[] encryptedData = Encrypt(clearBytes, key, iv);

        // Convert to Base64 string
        return Convert.ToBase64String(encryptedData);
    }

    // Encrypt bytes into bytes using a password 
    public static byte[] Encrypt(byte[] clearData, string Password)
    {
        // Get key and IV exactly as original code did
        GetKeyAndIV(Password, Salt, out byte[] key, out byte[] iv);

        return Encrypt(clearData, key, iv);
    }

    // Encrypt a file into another file using a password 
    public static void Encrypt(string fileIn, string fileOut, string Password)
    {
        // Open file streams
        using (var fsIn = new FileStream(fileIn, FileMode.Open, FileAccess.Read))
        using (var fsOut = new FileStream(fileOut, FileMode.OpenOrCreate, FileAccess.Write))
        {
            // Get key and IV exactly as original code did
            GetKeyAndIV(Password, Salt, out byte[] key, out byte[] iv);

            using (var aes = Aes.Create())
            {
                aes.Key = key;
                aes.IV = iv;

                // Create crypto stream for encryption
                using (var cs = new CryptoStream(fsOut, aes.CreateEncryptor(), CryptoStreamMode.Write))
                {
                    // Process file in chunks to handle large files efficiently
                    int bufferLen = 4096;
                    byte[] buffer = new byte[bufferLen];
                    int bytesRead;

                    do
                    {
                        bytesRead = fsIn.Read(buffer, 0, bufferLen);
                        if (bytesRead > 0)
                        {
                            cs.Write(buffer, 0, bytesRead);
                        }
                    } while (bytesRead != 0);

                    cs.FlushFinalBlock();
                }
            }
        }
    }

    // Decrypt a byte array into a byte array using a key and an IV 
    public static byte[] Decrypt(byte[] cipherData, byte[] Key, byte[] IV)
    {
        // Create a MemoryStream to accept the decrypted bytes
        using (var ms = new MemoryStream())
        {
            // Create AES algorithm
            using (var aes = Aes.Create())
            {
                // Set the key and IV
                aes.Key = Key;
                aes.IV = IV;

                // Create a CryptoStream for decryption
                using (var cs = new CryptoStream(ms, aes.CreateDecryptor(), CryptoStreamMode.Write))
                {
                    // Write the data and perform decryption
                    cs.Write(cipherData, 0, cipherData.Length);
                    cs.FlushFinalBlock();
                }

                // Return the decrypted data
                return ms.ToArray();
            }
        }
    }

    // Decrypt a string into a string using a password 
    public static string Decrypt(string cipherText, string Password)
    {
        if (string.IsNullOrEmpty(cipherText))
        {
            return "";
        }
        // Convert Base64 string to byte array
        byte[] cipherBytes = Convert.FromBase64String(cipherText);

        // Get key and IV exactly as original code did
        GetKeyAndIV(Password, Salt, out byte[] key, out byte[] iv);

        // Decrypt the data
        byte[] decryptedData = Decrypt(cipherBytes, key, iv);

        // Convert back to Unicode string (same as original)
        return System.Text.Encoding.Unicode.GetString(decryptedData);
    }

    // Decrypt bytes into bytes using a password 
    public static byte[] Decrypt(byte[] cipherData, string Password)
    {
        // Get key and IV exactly as original code did
        GetKeyAndIV(Password, Salt, out byte[] key, out byte[] iv);

        return Decrypt(cipherData, key, iv);
    }

    // Decrypt a file into another file using a password 
    public static void Decrypt(string fileIn, string fileOut, string Password)
    {
        // Open file streams
        using (var fsIn = new FileStream(fileIn, FileMode.Open, FileAccess.Read))
        using (var fsOut = new FileStream(fileOut, FileMode.OpenOrCreate, FileAccess.Write))
        {
            // Get key and IV exactly as original code did
            GetKeyAndIV(Password, Salt, out byte[] key, out byte[] iv);

            using (var aes = Aes.Create())
            {
                aes.Key = key;
                aes.IV = iv;

                // Create crypto stream for decryption
                using (var cs = new CryptoStream(fsOut, aes.CreateDecryptor(), CryptoStreamMode.Write))
                {
                    // Process file in chunks
                    int bufferLen = 4096;
                    byte[] buffer = new byte[bufferLen];
                    int bytesRead;

                    do
                    {
                        bytesRead = fsIn.Read(buffer, 0, bufferLen);
                        if (bytesRead > 0)
                        {
                            cs.Write(buffer, 0, bytesRead);
                        }
                    } while (bytesRead != 0);

                    cs.FlushFinalBlock();
                }
            }
        }
    }
}