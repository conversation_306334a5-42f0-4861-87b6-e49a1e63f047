using System.Data;
using System.Data.SqlClient;
using Dapper;
using OfficeOpenXml;
using RaabtaSalaryCertificateApp.Components.Models;

namespace RaabtaSalaryCertificateApp.Components.Services;

public class RaabtaDataService
{
    private readonly IWebHostEnvironment _environment;
    private readonly ILogger<RaabtaDataService> _logger;
    private string conStr =
        "Password=**************;Persist Security Info=True;User ID=sa;Initial Catalog=JangRaabta;Data Source=*************\\roshni";
    public RaabtaDataService(IWebHostEnvironment environment, ILogger<RaabtaDataService> logger)
    {
        _environment = environment;
        _logger = logger;
        // Set EPPlus to non-commercial mode
        ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
    }

    public Task<List<Employee>> LoadEmployeesFromExcel(string employeeCode)
    {
        
        var con = new SqlConnection(conStr);
        var query = "dbo.rptEmployees";
        var pram = new { employeeCode };
        var employees = con.Query<Employee>(query, pram, commandType: CommandType.StoredProcedure).ToList();
        if (employeeCode != "")
            foreach (var emp in employees)
                // Decrypt sensitive fields if necessary
                // Example: emp.GrossSalary = EncDec.Decrypt(emp.GrossSalary, "a0uJ4P8%H&9P!bH8em@E#J>s");
                // Note: Ensure EncDec.Decrypt method is implemented correctly
                if (!string.IsNullOrEmpty(emp.GrossSalary))
                {
                    // Encrypt the GrossSalary field
                    // Assuming EncDec.Encrypt method is implemented correctly 
                    emp.GrossSalary = EncDec.Decrypt(emp.GrossSalary, "a0uJ4P8%H&9P!bH8em@E#J>s");
                    emp.MobileEntitlement = EncDec.Decrypt(emp.MobileEntitlement, "a0uJ4P8%H&9P!bH8em@E#J>s");
                    emp.PetrolEntitlement = EncDec.Decrypt(emp.PetrolEntitlement, "a0uJ4P8%H&9P!bH8em@E#J>s");
                    emp.CarConveyance = EncDec.Decrypt(emp.CarConveyance, "a0uJ4P8%H&9P!bH8em@E#J>s");
                    emp.Other = EncDec.Decrypt(emp.Other, "a0uJ4P8%H&9P!bH8em@E#J>s");
                    //emp.HouseRent = EncDec.Decrypt(emp.HouseRent, "a0uJ4P8%H&9P!bH8em@E#J>s");
                    //emp.Utilities = EncDec.Decrypt(emp.Utilities, "a0uJ4P8%H&9P!bH8em@E#J>s");
                    //emp.BasicSalary = EncDec.Decrypt(emp.BasicSalary, "a0uJ4P8%H&9P!bH8em@E#J>s");
                }


        return Task.FromResult(employees);
    }
    //public Task<List<Employee>> LoadEmployeesFromExcel2()
    //{
    //    var employees = new List<Employee>();
    //    var filePath = Path.Combine(_environment.ContentRootPath, "Data", "emp-6-Jun-2025.xlsx");

    //    try
    //    {
    //        using var package = new ExcelPackage(new FileInfo(filePath));
    //        var worksheet = package.Workbook.Worksheets[0];
    //        var rowCount = worksheet.Dimension.Rows;

    //        // Skip header row
    //        for (int row = 2; row <= rowCount; row++)
    //        {
    //            var employee = new Employee
    //            {
    //                EmployeeCode = worksheet.Cells[row, 1].Text,
    //                EmployeeName = worksheet.Cells[row, 2].Text,
    //                FatherName = worksheet.Cells[row, 3].Text,
    //                DateOfJoin = DateTime.TryParse(worksheet.Cells[row, 4].Text, out var date) ? date : null,
    //                Department = worksheet.Cells[row, 5].Text,
    //                Designation = worksheet.Cells[row, 6].Text,
    //                Station = worksheet.Cells[row, 7].Text,
    //                EmploymentStatus = worksheet.Cells[row, 8].Text,
    //                BasicSalary = decimal.TryParse(worksheet.Cells[row, 9].Text, out var basic) ? basic : 0,
    //                GrossSalary = decimal.TryParse(worksheet.Cells[row, 10].Text, out var gross) ? gross : 0,
    //                SalaryType = worksheet.Cells[row, 11].Text,
    //                Currency = worksheet.Cells[row, 12].Text,
    //                HouseRent = decimal.TryParse(worksheet.Cells[row, 13].Text, out var rent) ? rent : 0,
    //                Utilities = decimal.TryParse(worksheet.Cells[row, 14].Text, out var util) ? util : 0,
    //                PetrolEntitlement = worksheet.Cells[row, 15].Text,
    //                MobileEntitlement = worksheet.Cells[row, 16].Text,
    //                CarConveyance = worksheet.Cells[row, 17].Text,
    //                CarDescription = worksheet.Cells[row, 18].Text,
    //                Other = worksheet.Cells[row, 19].Text,
    //                CNIC = worksheet.Cells[row, 20].Text
    //            };
    //            employees.Add(employee);
    //        }
    //    }
    //    catch (Exception ex)
    //    {
    //        _logger.LogError(ex, "Error loading employees from Excel");
    //    }

    //    return Task.FromResult(employees);
    //}

    public Task<bool> CanAccessCertificate(string userId, string certificateType)
    {
        var con = new SqlConnection(conStr);
        var da = new SqlDataAdapter("dbo.CanAccessCertificate", con);
        da.SelectCommand.CommandType = CommandType.StoredProcedure;
        da.SelectCommand.Parameters.AddWithValue("@UserId", userId);
        da.SelectCommand.Parameters.AddWithValue("@CertificateType", certificateType);
        var dt = new DataTable();
        da.Fill(dt);
        return Task.FromResult(dt.Rows.Count > 0);
    }
}