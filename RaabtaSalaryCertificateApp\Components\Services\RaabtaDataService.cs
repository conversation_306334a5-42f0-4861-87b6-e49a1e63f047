using System.Data;
using Microsoft.Data.SqlClient;
using Dapper;
using OfficeOpenXml;
using RaabtaSalaryCertificateApp.Components.Models;

namespace RaabtaSalaryCertificateApp.Components.Services;

public class RaabtaDataService
{
    private readonly ILogger<RaabtaDataService> _logger;
    private readonly string _connectionString;

    public RaabtaDataService(ILogger<RaabtaDataService> logger, IConfiguration configuration)
    {
        _logger = logger;
        _connectionString = configuration.GetConnectionString("DefaultConnection")
            ?? throw new InvalidOperationException("DefaultConnection string not found.");

        // Set EPPlus to non-commercial mode
        ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
    }

    public Task<List<Employee>> LoadEmployeesFromExcel(string employeeCode)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            var query = "dbo.rptEmployees";
            var parameters = new { employeeCode };

            var employees = connection.Query<Employee>(query, parameters, commandType: CommandType.StoredProcedure).ToList();

            if (!string.IsNullOrEmpty(employeeCode))
            {
                foreach (var emp in employees)
                {
                    // Decrypt sensitive fields if necessary
                    if (!string.IsNullOrEmpty(emp.GrossSalary))
                    {
                        emp.GrossSalary = EncDec.Decrypt(emp.GrossSalary ?? "", "a0uJ4P8%H&9P!bH8em@E#J>s");
                        emp.MobileEntitlement = EncDec.Decrypt(emp.MobileEntitlement ?? "", "a0uJ4P8%H&9P!bH8em@E#J>s");
                        emp.PetrolEntitlement = EncDec.Decrypt(emp.PetrolEntitlement ?? "", "a0uJ4P8%H&9P!bH8em@E#J>s");
                        emp.CarConveyance = EncDec.Decrypt(emp.CarConveyance ?? "", "a0uJ4P8%H&9P!bH8em@E#J>s");
                        emp.Other = EncDec.Decrypt(emp.Other ?? "", "a0uJ4P8%H&9P!bH8em@E#J>s");
                        //emp.HouseRent = EncDec.Decrypt(emp.HouseRent ?? "", "a0uJ4P8%H&9P!bH8em@E#J>s");
                        //emp.Utilities = EncDec.Decrypt(emp.Utilities ?? "", "a0uJ4P8%H&9P!bH8em@E#J>s");
                        //emp.BasicSalary = EncDec.Decrypt(emp.BasicSalary ?? "", "a0uJ4P8%H&9P!bH8em@E#J>s");
                    }
                }
            }

            return Task.FromResult( employees);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading employees from database for employeeCode: {EmployeeCode}", employeeCode);
            throw;
        }
    }
    //public Task<List<Employee>> LoadEmployeesFromExcel2()
    //{
    //    var employees = new List<Employee>();
    //    var filePath = Path.Combine(_environment.ContentRootPath, "Data", "emp-6-Jun-2025.xlsx");

    //    try
    //    {
    //        using var package = new ExcelPackage(new FileInfo(filePath));
    //        var worksheet = package.Workbook.Worksheets[0];
    //        var rowCount = worksheet.Dimension.Rows;

    //        // Skip header row
    //        for (int row = 2; row <= rowCount; row++)
    //        {
    //            var employee = new Employee
    //            {
    //                EmployeeCode = worksheet.Cells[row, 1].Text,
    //                EmployeeName = worksheet.Cells[row, 2].Text,
    //                FatherName = worksheet.Cells[row, 3].Text,
    //                DateOfJoin = DateTime.TryParse(worksheet.Cells[row, 4].Text, out var date) ? date : null,
    //                Department = worksheet.Cells[row, 5].Text,
    //                Designation = worksheet.Cells[row, 6].Text,
    //                Station = worksheet.Cells[row, 7].Text,
    //                EmploymentStatus = worksheet.Cells[row, 8].Text,
    //                BasicSalary = decimal.TryParse(worksheet.Cells[row, 9].Text, out var basic) ? basic : 0,
    //                GrossSalary = decimal.TryParse(worksheet.Cells[row, 10].Text, out var gross) ? gross : 0,
    //                SalaryType = worksheet.Cells[row, 11].Text,
    //                Currency = worksheet.Cells[row, 12].Text,
    //                HouseRent = decimal.TryParse(worksheet.Cells[row, 13].Text, out var rent) ? rent : 0,
    //                Utilities = decimal.TryParse(worksheet.Cells[row, 14].Text, out var util) ? util : 0,
    //                PetrolEntitlement = worksheet.Cells[row, 15].Text,
    //                MobileEntitlement = worksheet.Cells[row, 16].Text,
    //                CarConveyance = worksheet.Cells[row, 17].Text,
    //                CarDescription = worksheet.Cells[row, 18].Text,
    //                Other = worksheet.Cells[row, 19].Text,
    //                CNIC = worksheet.Cells[row, 20].Text
    //            };
    //            employees.Add(employee);
    //        }
    //    }
    //    catch (Exception ex)
    //    {
    //        _logger.LogError(ex, "Error loading employees from Excel");
    //    }

    //    return Task.FromResult(employees);
    //}

    public Task<bool> CanAccessCertificate(string userId, string certificateType)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            var query = "dbo.CanAccessCertificate";
            var parameters = new { UserId = userId, CertificateType = certificateType };

            var result = connection.QueryFirstOrDefault<int?>(query, parameters, commandType: CommandType.StoredProcedure);
            return Task.FromResult( result is > 0);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking certificate access for user: {UserId}, certificateType: {CertificateType}", userId, certificateType);
            return Task.FromResult(false); // Default to no access on error
        }
    }

    //public async Task<List<string>> GetUserCertificateTypes(string userId)
    //{
    //    try
    //    {
    //        using var connection = new SqlConnection(_connectionString);
    //        var query = "select cu.CertificateType FROM [10.1.10.124].[Virtual domain].dbo.CertificateUsers cu WHERE cu.UserId=@UserId";
    //        var parameters = new { UserId = userId };

    //        var certificateTypes = await connection.QueryAsync<string>(query, parameters);
    //        return [.. certificateTypes];
    //    }
    //    catch (Exception ex)
    //    {
    //        _logger.LogError(ex, "Error getting certificate types for user: {UserId}", userId);
    //        return [];
    //    }
    //}
}