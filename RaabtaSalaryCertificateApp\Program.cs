using Microsoft.FluentUI.AspNetCore.Components;
using RaabtaSalaryCertificateApp.Components;
using RaabtaSalaryCertificateApp.Components.Services;
using Microsoft.AspNetCore.Authentication.Negotiate;
using Syncfusion.Blazor;

Syncfusion.Licensing.SyncfusionLicenseProvider.RegisterLicense("MzkwMzk1NEAzMjM5MmUzMDJlMzAzYjMyMzkzYktraUluY0htbWI3ZHZjWEhFeDhndVNBbzZlOG9PUEdnVERYbHdvcW9rTU09");

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddRazorComponents()
    .AddInteractiveServerComponents();

builder.Services.AddSyncfusionBlazor();

builder.Services.AddFluentUIComponents();

// Add Windows Authentication
builder.Services.AddAuthentication(NegotiateDefaults.AuthenticationScheme)
    .AddNegotiate();
builder.Services.AddAuthorization(options => { options.FallbackPolicy = options.DefaultPolicy; });

// Register services
builder.Services.AddScoped<RaabtaDataService>();

var app = builder.Build();

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Error", createScopeForErrors: true);
    app.UseHsts();
}

app.UseHttpsRedirection();
app.UseAntiforgery();
app.UseAuthentication(); 
app.UseAuthorization();

app.MapStaticAssets();
app.MapRazorComponents<App>()
    .AddInteractiveServerRenderMode();

app.Run();
