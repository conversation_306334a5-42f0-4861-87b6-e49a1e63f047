{"$schema": "https://json.schemastore.org/launchsettings.json", "iisSettings": {"windowsAuthentication": true, "anonymousAuthentication": false, "iisExpress": {"applicationUrl": "http://localhost:11938", "sslPort": 44390}}, "profiles": {"http": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": true, "applicationUrl": "http://localhost:5238", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "https": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": true, "applicationUrl": "https://localhost:7227;http://localhost:5238", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}}}