<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="dapper" Version="2.1.66" />
    <PackageReference Include="Microsoft.AspNetCore.Components.QuickGrid" Version="9.0.5" />
    <PackageReference Include="Microsoft.Data.SqlClient" Version="6.0.2" />
    <PackageReference Include="Microsoft.FluentUI.AspNetCore.Components" Version="4.11.5" />
    <PackageReference Include="Microsoft.FluentUI.AspNetCore.Components.Icons" Version="4.11.5" />
    <PackageReference Include="EPPlus" Version="7.0.10" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.Negotiate" Version="9.0.2" />
    <PackageReference Include="syncfusion.blazor" Version="29.2.10" />
    <PackageReference Include="syncfusion.blazor.themes" Version="29.2.10" />
  </ItemGroup>
  
  <ItemGroup>
    <Folder Include="Data\" />
  </ItemGroup>
</Project>
